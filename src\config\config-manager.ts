import { promises as fs } from 'fs';
import path from 'path';
import { Logger } from 'winston';
import { Config, ConfigSchema, LLMProvider } from '@/types';

export class ConfigManager {
  private config: Config;
  private readonly logger: Logger;
  private readonly configPath: string;

  constructor(logger: Logger, configName = 'agentic-cli-config') {
    this.logger = logger;
    this.configPath = path.join(process.cwd(), '.agent-config', `${configName}.json`);

    // Initialize with default config
    this.config = {
      providers: [],
      session: {
        persistContext: true,
        maxHistorySize: 1000,
        autoSave: true,
      },
      ui: {
        theme: 'auto',
        verbosity: 'normal',
        showProgress: true,
        colors: true,
      },
      tools: {
        allowDangerous: false,
        maxParallel: 5,
        timeout: 30000,
      },
    };

    this.logger.info('Config manager initialized', { configPath: this.configPath });
  }

  async initialize(): Promise<void> {
    try {
      // Load existing config from file if it exists
      await this.loadConfigFromFile();

      // Validate loaded config
      ConfigSchema.parse(this.config);

      this.logger.info('Configuration loaded and validated successfully');
    } catch (error) {
      this.logger.warn('Configuration validation failed, using defaults', { error });
      await this.resetToDefaults();
    }

    // Ensure we have at least one provider configured
    if (this.getProviders().length === 0) {
      await this.setupDefaultProviders();
    }
  }

  private async loadConfigFromFile(): Promise<void> {
    try {
      // Ensure config directory exists
      await fs.mkdir(path.dirname(this.configPath), { recursive: true });

      // Try to read existing config
      const configData = await fs.readFile(this.configPath, 'utf8');
      const loadedConfig = JSON.parse(configData);

      // Merge with defaults to ensure all properties exist
      this.config = { ...this.config, ...loadedConfig };
    } catch (error) {
      // File doesn't exist or is invalid, use defaults
      this.logger.info('No existing config found, using defaults');
    }
  }

  private async saveConfigToFile(): Promise<void> {
    try {
      // Ensure config directory exists
      await fs.mkdir(path.dirname(this.configPath), { recursive: true });

      // Write config to file
      const configJson = JSON.stringify(this.config, null, 2);
      await fs.writeFile(this.configPath, configJson, 'utf8');

      this.logger.debug('Configuration saved to file', { configPath: this.configPath });
    } catch (error) {
      this.logger.error('Failed to save configuration to file', { error, configPath: this.configPath });
      throw error;
    }
  }

  getConfig(): Config {
    return this.config;
  }

  async updateConfig(updates: Partial<Config>): Promise<void> {
    try {
      const currentConfig = this.getConfig();
      const newConfig = { ...currentConfig, ...updates };

      // Validate the new configuration
      ConfigSchema.parse(newConfig);

      // Update the configuration
      this.config = newConfig;

      // Save to file
      await this.saveConfigToFile();

      this.logger.info('Configuration updated', { updates });
    } catch (error) {
      this.logger.error('Failed to update configuration', { error, updates });
      throw error;
    }
  }

  // Provider management
  getProviders(): LLMProvider[] {
    return this.config.providers || [];
  }

  async addProvider(provider: LLMProvider): Promise<void> {
    const providers = this.getProviders();
    
    // Check if provider already exists
    if (providers.some(p => p.name === provider.name)) {
      throw new Error(`Provider with name '${provider.name}' already exists`);
    }

    providers.push(provider);
    await this.updateConfig({ providers });
    
    this.logger.info('Provider added', { name: provider.name, type: provider.type });
  }

  async updateProvider(name: string, updates: Partial<LLMProvider>): Promise<void> {
    const providers = this.getProviders();
    const index = providers.findIndex(p => p.name === name);
    
    if (index === -1) {
      throw new Error(`Provider '${name}' not found`);
    }

    providers[index] = { ...providers[index]!, ...updates };
    await this.updateConfig({ providers });
    
    this.logger.info('Provider updated', { name, updates });
  }

  async removeProvider(name: string): Promise<void> {
    const providers = this.getProviders();
    const filteredProviders = providers.filter(p => p.name !== name);
    
    if (filteredProviders.length === providers.length) {
      throw new Error(`Provider '${name}' not found`);
    }

    await this.updateConfig({ providers: filteredProviders });
    
    this.logger.info('Provider removed', { name });
  }

  getProvider(name: string): LLMProvider | undefined {
    return this.getProviders().find(p => p.name === name);
  }

  // Session configuration
  getSessionConfig(): Config['session'] {
    return this.config.session;
  }

  async updateSessionConfig(updates: Partial<Config['session']>): Promise<void> {
    const currentSession = this.getSessionConfig();
    const newSession = { ...currentSession, ...updates };
    await this.updateConfig({ session: newSession });
  }

  // UI configuration
  getUIConfig(): Config['ui'] {
    return this.config.ui;
  }

  async updateUIConfig(updates: Partial<Config['ui']>): Promise<void> {
    const currentUI = this.getUIConfig();
    const newUI = { ...currentUI, ...updates };
    await this.updateConfig({ ui: newUI });
  }

  // Tools configuration
  getToolsConfig(): Config['tools'] {
    return this.config.tools;
  }

  async updateToolsConfig(updates: Partial<Config['tools']>): Promise<void> {
    const currentTools = this.getToolsConfig();
    const newTools = { ...currentTools, ...updates };
    await this.updateConfig({ tools: newTools });
  }

  // Configuration file operations
  async exportConfig(filePath: string): Promise<void> {
    const config = this.getConfig();
    const configJson = JSON.stringify(config, null, 2);
    
    await fs.writeFile(filePath, configJson, 'utf8');
    this.logger.info('Configuration exported', { filePath });
  }

  async importConfig(filePath: string): Promise<void> {
    try {
      const configJson = await fs.readFile(filePath, 'utf8');
      const importedConfig = JSON.parse(configJson);
      
      // Validate imported configuration
      const validatedConfig = ConfigSchema.parse(importedConfig);
      
      // Update configuration
      await this.updateConfig(validatedConfig);
      
      this.logger.info('Configuration imported', { filePath });
    } catch (error) {
      this.logger.error('Failed to import configuration', { error, filePath });
      throw error;
    }
  }

  async resetToDefaults(): Promise<void> {
    // Reset to default configuration
    this.config = {
      providers: [],
      session: {
        persistContext: true,
        maxHistorySize: 1000,
        autoSave: true,
      },
      ui: {
        theme: 'auto',
        verbosity: 'normal',
        showProgress: true,
        colors: true,
      },
      tools: {
        allowDangerous: false,
        maxParallel: 5,
        timeout: 30000,
      },
    };

    await this.setupDefaultProviders();
    await this.saveConfigToFile();
    this.logger.info('Configuration reset to defaults');
  }

  private async setupDefaultProviders(): Promise<void> {
    const defaultProviders: LLMProvider[] = [
      {
        name: 'deepseek-coder',
        type: 'deepseek',
        model: 'deepseek-coder',
        maxTokens: 4000,
        temperature: 0.7,
        priority: 1,
        available: false,
      },
      {
        name: 'openai-gpt4',
        type: 'openai',
        model: 'gpt-4',
        maxTokens: 4000,
        temperature: 0.7,
        priority: 2,
        available: false,
      },
      {
        name: 'anthropic-claude',
        type: 'anthropic',
        model: 'claude-3-sonnet-20240229',
        maxTokens: 4000,
        temperature: 0.7,
        priority: 3,
        available: false,
      },
      {
        name: 'google-gemini',
        type: 'google',
        model: 'gemini-pro',
        maxTokens: 4000,
        temperature: 0.7,
        priority: 4,
        available: false,
      },
      {
        name: 'mistral-7b',
        type: 'mistral',
        model: 'mistral-medium',
        maxTokens: 4000,
        temperature: 0.7,
        priority: 5,
        available: false,
      },
      {
        name: 'ollama-local',
        type: 'ollama',
        baseUrl: 'http://localhost:11434',
        model: 'llama2',
        maxTokens: 4000,
        temperature: 0.7,
        priority: 6,
        available: false,
      },
    ];

    await this.updateConfig({ providers: defaultProviders });
    this.logger.info('Default providers configured');
  }

  // Utility methods
  getConfigPath(): string {
    return this.configPath;
  }

  async validateConfig(): Promise<{ valid: boolean; errors?: string[] }> {
    try {
      const config = this.getConfig();
      ConfigSchema.parse(config);
      return { valid: true };
    } catch (error) {
      const errors = error instanceof Error ? [error.message] : ['Unknown validation error'];
      return { valid: false, errors };
    }
  }

  async backupConfig(): Promise<string> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupPath = `${this.configPath}.backup.${timestamp}`;
    
    await fs.copyFile(this.configPath, backupPath);
    this.logger.info('Configuration backed up', { backupPath });
    
    return backupPath;
  }

  // Environment variable integration
  async loadFromEnvironment(): Promise<void> {
    const envMappings = {
      'OPENAI_API_KEY': { provider: 'openai-gpt4', field: 'apiKey' },
      'ANTHROPIC_API_KEY': { provider: 'anthropic-claude', field: 'apiKey' },
      'GOOGLE_API_KEY': { provider: 'google-gemini', field: 'apiKey' },
      'MISTRAL_API_KEY': { provider: 'mistral-7b', field: 'apiKey' },
      'DEEPSEEK_API_KEY': { provider: 'deepseek-coder', field: 'apiKey' },
    };

    const providers = this.getProviders();
    let updated = false;

    Object.entries(envMappings).forEach(([envVar, mapping]) => {
      const value = process.env[envVar];
      if (value) {
        const provider = providers.find(p => p.name === mapping.provider);
        if (provider) {
          (provider as any)[mapping.field] = value;
          updated = true;
          this.logger.info(`Loaded ${mapping.field} for ${mapping.provider} from environment`);
        }
      }
    });

    if (updated) {
      this.config.providers = providers;
      await this.saveConfigToFile();
    }
  }
}
