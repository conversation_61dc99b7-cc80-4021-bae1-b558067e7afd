import MistralClient from '@mistralai/mistralai';
import { Logger } from 'winston';
import { LLMProvider, LLMMessage, LLMResponse, ProviderError } from '@/types';
import { ProviderInterface, GenerationOptions } from './llm-provider-manager';

export class <PERSON>stralProvider implements ProviderInterface {
  public readonly name: string;
  public readonly type: LLMProvider['type'] = 'mistral';
  
  private client: MistralClient;
  private config: <PERSON><PERSON><PERSON>ider;
  private logger: Logger;

  constructor(config: <PERSON><PERSON><PERSON>ider, logger: Logger) {
    this.name = config.name;
    this.config = config;
    this.logger = logger;

    if (!config.apiKey) {
      this.logger.warn(`Mistral API key not provided for ${config.name}`);
      // Create a dummy client that will fail isAvailable() check
      this.client = new MistralClient('dummy-key');
      return;
    }

    this.client = new MistralClient(config.apiKey);
  }

  async isAvailable(): Promise<boolean> {
    try {
      await this.client.listModels();
      return true;
    } catch (error) {
      this.logger.warn(`Mistral provider ${this.name} not available`, { error });
      return false;
    }
  }

  async generateResponse(
    messages: LLMMessage[], 
    options: GenerationOptions = {}
  ): Promise<LLMResponse> {
    try {
      const mistralMessages = this.convertMessages(messages);
      
      const response = await this.client.chat({
        model: this.config.model,
        messages: mistralMessages,
        temperature: options.temperature ?? this.config.temperature,
        maxTokens: options.maxTokens ?? this.config.maxTokens,
        ...(options.topP !== undefined && { topP: options.topP }),
      });

      const choice = response.choices[0];
      if (!choice) {
        throw new ProviderError('No response choice returned', this.name);
      }

      return {
        content: choice.message.content || '',
        usage: response.usage ? {
          promptTokens: response.usage.prompt_tokens,
          completionTokens: response.usage.completion_tokens,
          totalTokens: response.usage.total_tokens,
        } : undefined,
        finishReason: 'stop',
      };
    } catch (error) {
      throw new ProviderError(`Mistral request failed: ${(error as Error).message}`, this.name);
    }
  }

  private convertMessages(messages: LLMMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role === 'tool' ? 'assistant' : msg.role,
      content: msg.content,
    }));
  }

  estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }

  getMaxTokens(): number {
    return this.config.maxTokens;
  }
}
