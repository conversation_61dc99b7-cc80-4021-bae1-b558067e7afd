import axios, { AxiosInstance } from 'axios';
import { Logger } from 'winston';
import { LLMProvider, LLMMessage, LLMResponse, ProviderError } from '@/types';
import { ProviderInterface, GenerationOptions } from './llm-provider-manager';

export class DeepSeekProvider implements ProviderInterface {
  public readonly name: string;
  public readonly type: LLMProvider['type'] = 'deepseek';
  
  private client: AxiosInstance;
  private config: <PERSON><PERSON><PERSON>ider;
  private logger: Logger;

  constructor(config: LL<PERSON>rovider, logger: Logger) {
    this.name = config.name;
    this.config = config;
    this.logger = logger;

    if (!config.apiKey) {
      this.logger.warn(`DeepSeek API key not provided for ${config.name}`);
      // Create a dummy client that will fail isAvailable() check
      this.client = axios.create({
        baseURL: config.baseUrl || 'https://api.deepseek.com/v1',
        headers: {
          'Authorization': 'Bearer dummy-key',
          'Content-Type': 'application/json',
        },
        timeout: 30000,
      });
      return;
    }

    this.client = axios.create({
      baseURL: config.baseUrl || 'https://api.deepseek.com/v1',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
      timeout: 30000,
    });
  }

  async isAvailable(): Promise<boolean> {
    try {
      await this.client.get('/models');
      return true;
    } catch (error) {
      this.logger.warn(`DeepSeek provider ${this.name} not available`, { error });
      return false;
    }
  }

  async generateResponse(
    messages: LLMMessage[], 
    options: GenerationOptions = {}
  ): Promise<LLMResponse> {
    try {
      const response = await this.client.post('/chat/completions', {
        model: this.config.model,
        messages: this.convertMessages(messages),
        temperature: options.temperature ?? this.config.temperature,
        max_tokens: options.maxTokens ?? this.config.maxTokens,
        top_p: options.topP,
        frequency_penalty: options.frequencyPenalty,
        presence_penalty: options.presencePenalty,
        stop: options.stop,
        stream: false,
      });

      const choice = response.data.choices[0];
      if (!choice) {
        throw new ProviderError('No response choice returned', this.name);
      }

      return {
        content: choice.message.content || '',
        usage: response.data.usage ? {
          promptTokens: response.data.usage.prompt_tokens,
          completionTokens: response.data.usage.completion_tokens,
          totalTokens: response.data.usage.total_tokens,
        } : undefined,
        finishReason: 'stop',
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new ProviderError(
          `DeepSeek API error: ${error.response?.data?.error?.message || error.message}`,
          this.name,
          { status: error.response?.status }
        );
      }
      throw new ProviderError(`DeepSeek request failed: ${(error as Error).message}`, this.name);
    }
  }

  private convertMessages(messages: LLMMessage[]): any[] {
    return messages.map(msg => ({
      role: msg.role === 'tool' ? 'assistant' : msg.role,
      content: msg.content,
    }));
  }

  estimateTokens(text: string): number {
    return Math.ceil(text.length / 4);
  }

  getMaxTokens(): number {
    return this.config.maxTokens;
  }
}
