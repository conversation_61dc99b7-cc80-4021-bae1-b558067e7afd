import { EventEmitter } from 'events';
import { nanoid } from 'nanoid';
import { Logger } from 'winston';
import PQueue from 'p-queue';
import {
  AgentTask,
  AgentPlan,
  AgentContext,
  AgentAction,
  LLMMessage,
  ToolResult,
  AgentError
} from '@/types';
import { LLMProviderManager } from '@core/providers/llm-provider-manager';
import { ToolRegistry } from '@tools/tool-registry';
import { ContextManager } from '@core/context/context-manager';
import { ErrorRecoveryManager } from '@core/recovery/error-recovery';

export class AutonomousAgent extends EventEmitter {
  private readonly queue: PQueue;
  private readonly logger: Logger;
  private readonly providerManager: LLMProviderManager;
  private readonly toolRegistry: ToolRegistry;
  private readonly contextManager: ContextManager;
  private readonly errorRecovery: ErrorRecoveryManager;
  private currentPlan?: AgentPlan | undefined;
  private isRunning = false;

  constructor(
    logger: Logger,
    providerManager: LLMProviderManager,
    toolRegistry: ToolRegistry,
    contextManager: ContextManager,
    maxConcurrency = 5
  ) {
    super();
    this.logger = logger;
    this.providerManager = providerManager;
    this.toolRegistry = toolRegistry;
    this.contextManager = contextManager;
    this.errorRecovery = new ErrorRecoveryManager(logger);
    this.queue = new PQueue({ concurrency: maxConcurrency });
  }

  async executeGoal(goal: string, context: AgentContext): Promise<void> {
    if (this.isRunning) {
      throw new AgentError('Agent is already running', 'AGENT_BUSY');
    }

    this.isRunning = true;
    this.emit('started', { goal });

    try {
      // Step 1: Create autonomous plan
      const plan = await this.createPlan(goal, context);
      this.currentPlan = plan;
      this.emit('planCreated', { plan });

      // Step 2: Execute plan with autonomous decision making
      await this.executePlan(plan, context);

      this.emit('completed', { goal, plan });
    } catch (error) {
      this.logger.error('Agent execution failed', { error, goal });
      this.emit('error', { error, goal });
      throw error;
    } finally {
      this.isRunning = false;
      this.currentPlan = undefined;
    }
  }

  private async createPlan(goal: string, context: AgentContext): Promise<AgentPlan> {
    this.logger.info('Creating autonomous plan', { goal });

    const systemPrompt = this.buildPlanningPrompt(context);
    const userPrompt = `Create a detailed execution plan for: ${goal}

    Analyze the current context and break down this goal into specific, actionable tasks.
    Consider:
    1. Task dependencies and optimal execution order
    2. Opportunities for parallel execution
    3. Required tools and operations
    4. Error handling and recovery strategies
    5. Estimated time and complexity

    Return a JSON plan with tasks that can be executed autonomously.`;

    const messages: LLMMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    const response = await this.providerManager.generateResponse(messages, {
      temperature: 0.3,
      maxTokens: 2000
    });

    try {
      // Extract JSON from markdown code blocks if present
      const jsonContent = this.extractJsonFromResponse(response.content);
      const planData = JSON.parse(jsonContent) as {
        tasks: Array<{
          type: AgentTask['type'];
          description: string;
          priority: number;
          dependencies: string[];
          toolName?: string;
          parameters?: unknown;
        }>;
        parallelizable: boolean;
        estimatedDuration: number;
      };

      // Create tasks with proper dependency resolution
      const taskMap = new Map<string, string>(); // description/index -> id mapping
      const tasks: AgentTask[] = planData.tasks.map((task, index) => {
        const taskId = nanoid();
        const taskKey = task.description || `task-${index}`;
        taskMap.set(taskKey, taskId);
        taskMap.set(index.toString(), taskId); // Also map by index

        return {
          id: taskId,
          type: task.type,
          description: task.description,
          priority: task.priority,
          dependencies: [], // Will be resolved below
          status: 'pending',
          createdAt: new Date(),
          updatedAt: new Date(),
        };
      });

      // Resolve dependencies after all tasks are created
      tasks.forEach((task, index) => {
        const originalTask = planData.tasks[index];
        if (originalTask && originalTask.dependencies && originalTask.dependencies.length > 0) {
          task.dependencies = originalTask.dependencies
            .map(dep => {
              // Try to resolve dependency by description first, then by index
              return taskMap.get(dep) || taskMap.get(dep.toString()) || '';
            })
            .filter(dep => dep && dep !== task.id); // Remove empty and self-dependencies
        }
      });

      return {
        id: nanoid(),
        goal,
        tasks,
        parallelizable: planData.parallelizable,
        estimatedDuration: planData.estimatedDuration,
        createdAt: new Date(),
      };
    } catch (error) {
      throw new AgentError('Failed to parse plan from LLM response', 'PLAN_PARSE_ERROR', {
        response: response.content,
        error
      });
    }
  }

  private async executePlan(plan: AgentPlan, context: AgentContext): Promise<void> {
    this.logger.info('Executing plan', { planId: plan.id, taskCount: plan.tasks.length });

    // Update context with current plan
    await this.contextManager.updateContext({
      memory: {
        ...context.memory,
        facts: {
          ...context.memory.facts,
          currentPlan: plan
        }
      }
    });

    const completedTasks = new Set<string>();
    const failedTasks = new Set<string>();

    while (completedTasks.size + failedTasks.size < plan.tasks.length) {
      // Find tasks ready for execution
      const readyTasks = plan.tasks.filter(task => 
        task.status === 'pending' &&
        task.dependencies.every(depId => completedTasks.has(depId))
      );

      if (readyTasks.length === 0) {
        // Check for circular dependencies or all remaining tasks failed
        const pendingTasks = plan.tasks.filter(task => task.status === 'pending');
        if (pendingTasks.length > 0) {
          throw new AgentError('Circular dependency or all remaining tasks blocked', 'EXECUTION_DEADLOCK');
        }
        break;
      }

      // Execute tasks (parallel if plan allows it)
      if (plan.parallelizable && readyTasks.length > 1) {
        await this.executeTasksParallel(readyTasks, context, completedTasks, failedTasks);
      } else {
        await this.executeTasksSequential(readyTasks, context, completedTasks, failedTasks);
      }
    }

    if (failedTasks.size > 0) {
      throw new AgentError(`Plan execution failed: ${failedTasks.size} tasks failed`, 'PLAN_EXECUTION_FAILED');
    }
  }

  private async executeTasksParallel(
    tasks: AgentTask[],
    context: AgentContext,
    completedTasks: Set<string>,
    failedTasks: Set<string>
  ): Promise<void> {
    const promises = tasks.map(task => 
      this.queue.add(() => this.executeTask(task, context))
    );

    const results = await Promise.allSettled(promises);
    
    results.forEach((result, index) => {
      const task = tasks[index]!;
      if (result.status === 'fulfilled') {
        completedTasks.add(task.id);
        task.status = 'completed';
        task.result = result.value;
      } else {
        failedTasks.add(task.id);
        task.status = 'failed';
        task.error = result.reason as Error;
      }
      task.updatedAt = new Date();
    });
  }

  private async executeTasksSequential(
    tasks: AgentTask[],
    context: AgentContext,
    completedTasks: Set<string>,
    failedTasks: Set<string>
  ): Promise<void> {
    for (const task of tasks) {
      try {
        const result = await this.executeTask(task, context);
        completedTasks.add(task.id);
        task.status = 'completed';
        task.result = result;
      } catch (error) {
        failedTasks.add(task.id);
        task.status = 'failed';
        task.error = error as Error;
        
        // Autonomous error recovery
        const shouldContinue = await this.handleTaskFailure(task, error as Error, context);
        if (!shouldContinue) {
          throw error;
        }
      }
      task.updatedAt = new Date();
    }
  }

  private async executeTask(task: AgentTask, context: AgentContext): Promise<ToolResult> {
    this.logger.info('Executing task', { taskId: task.id, description: task.description });
    this.emit('taskStarted', { task });

    task.status = 'running';
    task.updatedAt = new Date();

    try {
      // Autonomous tool selection and execution
      const toolResult = await this.selectAndExecuteTool(task, context);
      
      // Record action in context
      const action: AgentAction = {
        id: nanoid(),
        type: task.type,
        input: task,
        output: toolResult,
        timestamp: new Date(),
        duration: Date.now() - task.updatedAt.getTime(),
        success: toolResult.success,
        error: toolResult.error || undefined,
      };

      context.history.push(action);
      this.emit('taskCompleted', { task, result: toolResult });

      return toolResult;
    } catch (error) {
      this.logger.error('Task execution failed', { taskId: task.id, error });
      this.emit('taskFailed', { task, error });
      throw error;
    }
  }

  private async selectAndExecuteTool(task: AgentTask, context: AgentContext): Promise<ToolResult> {
    // Use LLM to autonomously select and configure the appropriate tool
    const systemPrompt = `You are an autonomous agent executor. Select and configure the appropriate tool for the given task.

Available tools:
${this.toolRegistry.getAvailableTools().map(tool => 
  `- ${tool.name}: ${tool.description}`
).join('\n')}

Current context:
- Working directory: ${context.workingDirectory}
- Platform: ${context.environment.platform}
- Available files: ${context.projectStructure.files.slice(0, 10).map(f => f.path).join(', ')}

Return a JSON object with:
{
  "toolName": "selected_tool_name",
  "parameters": { /* tool parameters */ }
}`;

    const userPrompt = `Execute this task: ${task.description}
Task type: ${task.type}
Task ID: ${task.id}`;

    const messages: LLMMessage[] = [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt }
    ];

    const response = await this.providerManager.generateResponse(messages, {
      temperature: 0.1,
      maxTokens: 1000
    });

    try {
      // Extract JSON from markdown code blocks if present
      const jsonContent = this.extractJsonFromResponse(response.content);
      const toolConfig = JSON.parse(jsonContent) as {
        toolName: string;
        parameters: unknown;
      };

      const tool = this.toolRegistry.getTool(toolConfig.toolName);
      if (!tool) {
        throw new AgentError(`Tool not found: ${toolConfig.toolName}`, 'TOOL_NOT_FOUND');
      }

      return await tool.execute(toolConfig.parameters, context);
    } catch (error) {
      throw new AgentError('Failed to execute tool', 'TOOL_EXECUTION_ERROR', {
        task: task.id,
        response: response.content,
        error
      });
    }
  }

  private async handleTaskFailure(task: AgentTask, error: Error, context: AgentContext): Promise<boolean> {
    this.logger.warn('Task failed, attempting autonomous recovery', { taskId: task.id, error: error.message });

    try {
      // Attempt error recovery
      const recoveryResult = await this.errorRecovery.attemptRecovery(error, task, context);

      if (recoveryResult?.success) {
        this.logger.info('Error recovery successful', { taskId: task.id });
        task.result = recoveryResult;
        return true; // Continue execution
      } else if (recoveryResult?.metadata?.['retryable']) {
        this.logger.info('Error recovery suggests retry', { taskId: task.id });

        // Attempt retry with exponential backoff
        try {
          const retryResult = await this.errorRecovery.executeWithRetry(
            () => this.executeTask(task, context),
            { maxAttempts: 2, baseDelay: 1000 },
            { taskId: task.id, description: task.description }
          );

          task.result = retryResult;
          return true; // Continue execution
        } catch (retryError) {
          this.logger.error('Retry failed', { taskId: task.id, retryError });
        }
      }
    } catch (recoveryError) {
      this.logger.error('Error recovery failed', { taskId: task.id, recoveryError });
    }

    return false; // Stop execution on unrecoverable failure
  }

  private extractJsonFromResponse(content: string): string {
    // Remove markdown code blocks if present
    const codeBlockRegex = /```(?:json)?\s*([\s\S]*?)\s*```/;
    const match = content.match(codeBlockRegex);

    if (match && match[1]) {
      return match[1].trim();
    }

    // If no code blocks found, return the original content
    return content.trim();
  }

  private buildPlanningPrompt(context: AgentContext): string {
    return `You are an autonomous AI agent with advanced planning capabilities. You can execute complex tasks by breaking them down into smaller, manageable steps.

Current Context:
- Working Directory: ${context.workingDirectory}
- Platform: ${context.environment.platform}
- Node Version: ${context.environment.nodeVersion}
- Available Tools: ${this.toolRegistry.getAvailableTools().map(t => t.name).join(', ')}
- Project Structure: ${context.projectStructure.files.length} files, ${context.projectStructure.directories.length} directories

Your capabilities include:
1. File system operations (read, write, create, delete, move, copy, permissions)
2. Shell command execution with full system access
3. Code analysis and generation
4. Project structure analysis
5. Parallel task execution when safe and beneficial
6. Autonomous error recovery and adaptation

When creating plans:
- Break complex goals into specific, actionable tasks
- For simple tasks (like creating a single file), create just one task
- Use dependencies sparingly - only when one task truly depends on another's output
- Dependencies should reference task indices (0, 1, 2, etc.) or exact task descriptions
- Optimize for parallel execution when possible
- Include error handling and recovery strategies
- Estimate realistic timeframes
- Consider safety and reversibility of operations

Return plans as JSON with this structure:
{
  "tasks": [
    {
      "type": "filesystem|shell|analysis|planning|execution",
      "description": "Specific task description",
      "priority": 1-10,
      "dependencies": [],
      "toolName": "suggested_tool",
      "parameters": {}
    }
  ],
  "parallelizable": true/false,
  "estimatedDuration": seconds
}

IMPORTANT: For simple file operations, create a single task with no dependencies.`;
  }

  stop(): void {
    this.isRunning = false;
    this.queue.clear();
    this.emit('stopped');
  }

  getCurrentPlan(): AgentPlan | undefined {
    return this.currentPlan;
  }

  isAgentRunning(): boolean {
    return this.isRunning;
  }
}
