{"providers": [{"name": "deepseek-chat", "type": "deepseek", "model": "deepseek-chat", "maxTokens": 4000, "temperature": 0.7, "priority": 1, "available": false, "apiKey": "sk-931c6a1daf1a4a299fea41bd5ac78e49"}, {"name": "openai-gpt4", "type": "openai", "model": "gpt-4", "maxTokens": 4000, "temperature": 0.7, "priority": 2, "available": false}, {"name": "anthropic-claude", "type": "anthropic", "model": "claude-3-sonnet-20240229", "maxTokens": 4000, "temperature": 0.7, "priority": 3, "available": false}, {"name": "google-gemini", "type": "google", "model": "gemini-pro", "maxTokens": 4000, "temperature": 0.7, "priority": 4, "available": false}, {"name": "mistral-7b", "type": "mistral", "model": "mistral-medium", "maxTokens": 4000, "temperature": 0.7, "priority": 5, "available": false}, {"name": "ollama-local", "type": "ollama", "baseUrl": "http://localhost:11434", "model": "llama2", "maxTokens": 4000, "temperature": 0.7, "priority": 6, "available": false}], "session": {"persistContext": true, "maxHistorySize": 1000, "autoSave": true}, "ui": {"theme": "auto", "verbosity": "normal", "showProgress": true, "colors": true}, "tools": {"allowDangerous": false, "maxParallel": 5, "timeout": 30000}}