import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { LLMProvider, LLMMessage, LLMResponse, ProviderError } from '@/types';
import { OpenAIProvider } from './openai-provider';
import { AnthropicProvider } from './anthropic-provider';
import { GoogleProvider } from './google-provider';
import { MistralProvider } from './mistral-provider';
import { DeepSeekProvider } from './deepseek-provider';
import { OllamaProvider } from './ollama-provider';

export interface GenerationOptions {
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  stop?: string[];
  stream?: boolean;
}

export interface ProviderInterface {
  name: string;
  type: LLMProvider['type'];
  isAvailable(): Promise<boolean>;
  generateResponse(messages: LLMMessage[], options?: GenerationOptions): Promise<LLMResponse>;
  estimateTokens(text: string): number;
  getMaxTokens(): number;
}

export class LLMProviderManager extends EventEmitter {
  private providers: Map<string, ProviderInterface> = new Map();
  private providerConfigs: LLMProvider[] = [];
  private currentProvider: ProviderInterface | undefined;
  private failoverEnabled = true;
  private rateLimits: Map<string, { requests: number; resetTime: number }> = new Map();
  private readonly logger: Logger;

  constructor(logger: Logger) {
    super();
    this.logger = logger;
  }

  async initialize(providerConfigs: LLMProvider[]): Promise<void> {
    this.providerConfigs = providerConfigs;
    
    for (const config of providerConfigs) {
      try {
        const provider = await this.createProvider(config);
        this.providers.set(config.name, provider);
        this.logger.info(`Initialized provider: ${config.name}`, { type: config.type });
      } catch (error) {
        this.logger.error(`Failed to initialize provider: ${config.name}`, { error });
      }
    }

    // Set primary provider
    await this.selectBestProvider();
  }

  private async createProvider(config: LLMProvider): Promise<ProviderInterface> {
    switch (config.type) {
      case 'openai':
        return new OpenAIProvider(config, this.logger);
      case 'anthropic':
        return new AnthropicProvider(config, this.logger);
      case 'google':
        return new GoogleProvider(config, this.logger);
      case 'mistral':
        return new MistralProvider(config, this.logger);
      case 'deepseek':
        return new DeepSeekProvider(config, this.logger);
      case 'ollama':
        return new OllamaProvider(config, this.logger);
      default:
        throw new ProviderError(`Unsupported provider type: ${config.type}`, config.name);
    }
  }

  async generateResponse(
    messages: LLMMessage[],
    options: GenerationOptions = {}
  ): Promise<LLMResponse> {
    if (!this.currentProvider) {
      const availableProviders = this.getAvailableProviders();
      if (availableProviders.length === 0) {
        throw new ProviderError(
          'No LLM providers are configured. Please add at least one provider with a valid API key using: agent-cli provider add <name> <type> <model> --api-key <key>',
          'NO_PROVIDERS_CONFIGURED'
        );
      } else {
        throw new ProviderError(
          `No LLM providers are available. Configured providers: ${availableProviders.join(', ')}. Please check your API keys and network connectivity.`,
          'NO_PROVIDERS_AVAILABLE'
        );
      }
    }

    const startTime = Date.now();
    let lastError: Error | undefined;

    // Try current provider first
    try {
      await this.checkRateLimit(this.currentProvider.name);
      const response = await this.currentProvider.generateResponse(messages, options);
      this.updateRateLimit(this.currentProvider.name);
      
      this.logger.debug('LLM response generated', {
        provider: this.currentProvider.name,
        duration: Date.now() - startTime,
        tokens: response.usage?.totalTokens
      });

      return response;
    } catch (error) {
      lastError = error as Error;
      this.logger.warn(`Provider ${this.currentProvider.name} failed`, { error });
      
      if (!this.failoverEnabled) {
        throw error;
      }
    }

    // Try failover providers
    const availableProviders = Array.from(this.providers.values())
      .filter(p => p.name !== this.currentProvider?.name);

    for (const provider of availableProviders) {
      try {
        if (!(await provider.isAvailable())) {
          continue;
        }

        await this.checkRateLimit(provider.name);
        const response = await provider.generateResponse(messages, options);
        this.updateRateLimit(provider.name);
        
        this.logger.info(`Failover to provider: ${provider.name}`);
        const previousProvider = this.currentProvider.name;
        this.currentProvider = provider;
        this.emit('providerSwitched', { from: previousProvider, to: provider.name });
        
        return response;
      } catch (error) {
        lastError = error as Error;
        this.logger.warn(`Failover provider ${provider.name} failed`, { error });
      }
    }

    throw new ProviderError(
      `All providers failed. Last error: ${lastError?.message}`,
      'ALL_FAILED',
      { lastError }
    );
  }

  async selectBestProvider(): Promise<void> {
    const sortedConfigs = this.providerConfigs
      .sort((a, b) => (b.priority ?? 1) - (a.priority ?? 1));

    for (const config of sortedConfigs) {
      const provider = this.providers.get(config.name);
      if (provider && await provider.isAvailable()) {
        this.currentProvider = provider;
        this.logger.info(`Selected provider: ${config.name}`);
        return;
      }
    }

    this.logger.warn('No available providers found - CLI will run with limited functionality');
    this.currentProvider = undefined;
  }

  private async checkRateLimit(providerName: string): Promise<void> {
    const limit = this.rateLimits.get(providerName);
    if (!limit) return;

    const now = Date.now();
    if (now < limit.resetTime && limit.requests >= 60) { // 60 requests per minute default
      const waitTime = limit.resetTime - now;
      throw new ProviderError(
        `Rate limit exceeded for ${providerName}. Wait ${waitTime}ms`,
        providerName,
        { waitTime }
      );
    }

    if (now >= limit.resetTime) {
      this.rateLimits.set(providerName, { requests: 0, resetTime: now + 60000 });
    }
  }

  private updateRateLimit(providerName: string): void {
    const limit = this.rateLimits.get(providerName);
    if (limit) {
      limit.requests++;
    } else {
      this.rateLimits.set(providerName, { 
        requests: 1, 
        resetTime: Date.now() + 60000 
      });
    }
  }

  getCurrentProvider(): ProviderInterface | undefined {
    return this.currentProvider;
  }

  getAvailableProviders(): string[] {
    return Array.from(this.providers.keys());
  }

  async switchProvider(providerName: string): Promise<void> {
    const provider = this.providers.get(providerName);
    if (!provider) {
      throw new ProviderError(`Provider not found: ${providerName}`, providerName);
    }

    if (!(await provider.isAvailable())) {
      throw new ProviderError(`Provider not available: ${providerName}`, providerName);
    }

    this.currentProvider = provider;
    this.logger.info(`Switched to provider: ${providerName}`);
  }

  setFailoverEnabled(enabled: boolean): void {
    this.failoverEnabled = enabled;
  }

  async healthCheck(): Promise<Record<string, boolean>> {
    const health: Record<string, boolean> = {};
    
    for (const [name, provider] of this.providers) {
      try {
        health[name] = await provider.isAvailable();
      } catch {
        health[name] = false;
      }
    }

    return health;
  }

  estimateTokens(text: string): number {
    if (!this.currentProvider) {
      // Rough estimation: ~4 characters per token
      return Math.ceil(text.length / 4);
    }
    return this.currentProvider.estimateTokens(text);
  }

  getMaxTokens(): number {
    if (!this.currentProvider) {
      return 4000; // Default fallback
    }
    return this.currentProvider.getMaxTokens();
  }

  async getProviderStats(): Promise<Record<string, {
    requests: number;
    failures: number;
    lastUsed?: Date;
    averageResponseTime?: number;
  }>> {
    // This would be enhanced with actual usage tracking
    // For now, return basic structure based on rate limits
    const stats: Record<string, any> = {};

    for (const [name] of this.providers.entries()) {
      const rateLimit = this.rateLimits.get(name);
      stats[name] = {
        requests: rateLimit?.requests || 0,
        failures: 0, // Would track actual failures
        lastUsed: rateLimit ? new Date(rateLimit.resetTime - 60000) : undefined,
        averageResponseTime: undefined, // Would track actual response times
      };
    }

    return stats;
  }

  async testProvider(providerName: string): Promise<{
    available: boolean;
    responseTime?: number;
    error?: string;
  }> {
    const provider = this.providers.get(providerName);
    if (!provider) {
      return { available: false, error: 'Provider not found' };
    }

    const startTime = Date.now();
    try {
      const available = await provider.isAvailable();
      const responseTime = Date.now() - startTime;
      return { available, responseTime };
    } catch (error) {
      const responseTime = Date.now() - startTime;
      return {
        available: false,
        responseTime,
        error: (error as Error).message
      };
    }
  }

  async shutdown(): Promise<void> {
    this.providers.clear();
    this.rateLimits.clear();
    this.currentProvider = undefined;
    this.removeAllListeners();
    this.logger.info('LLM Provider Manager shutdown');
  }
}
